"use client";

import React, { useState } from 'react';
import { format, addMonths, subMonths, startOfMonth, endOfMonth, isSameMonth, isSameDay, isAfter } from 'date-fns';
import { eachDayOfInterval } from 'date-fns/eachDayOfInterval';
import { CaretDown, CaretLeft, CaretRight } from '@phosphor-icons/react';

interface AnalyticsDateFilterProps {
  onApply: (startDate: Date, endDate: Date) => void;
}

type DateRange = 'This Month' | 'Last Month' | 'Custom';

// Helper function to format date range for display
const formatDateRange = (startDate: Date, endDate: Date): string => {
  const startFormatted = format(startDate, 'd MMM');
  const endFormatted = format(endDate, 'd MMM');
  return `${startFormatted} - ${endFormatted}`;
};

const AnalyticsDateFilter: React.FC<AnalyticsDateFilterProps> = ({ onApply }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedRange, setSelectedRange] = useState<DateRange>('This Month');
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDates, setSelectedDates] = useState<Date[]>([]);
  const [tempSelectedDates, setTempSelectedDates] = useState<Date[]>([]);

  const handleRangeSelect = (range: DateRange) => {
    setSelectedRange(range);
    const now = new Date();

    if (range === 'This Month') {
      const startDate = startOfMonth(now);
      // Use today's date instead of end of month to avoid future dates
      const endDate = now;
      onApply(startDate, endDate);
      setSelectedDates([startDate, endDate]);
      setIsOpen(false);
    } else if (range === 'Last Month') {
      const lastMonth = subMonths(now, 1);
      const startDate = startOfMonth(lastMonth);
      const endDate = endOfMonth(lastMonth);
      onApply(startDate, endDate);
      setSelectedDates([startDate, endDate]);
      setIsOpen(false);
    } else {
      // For Custom, clear the selections
      setTempSelectedDates([]);
    }
  };

  const handleDateClick = (date: Date) => {
    // Don't allow selecting future dates
    if (isAfter(date, new Date())) {
      return;
    }
    
    if (selectedRange === 'Custom') {
      if (tempSelectedDates.length === 0 || tempSelectedDates.length === 2) {
        setTempSelectedDates([date]);
      } else {
        const [start] = tempSelectedDates;
        const newDates = [start, date].sort((a, b) => a.getTime() - b.getTime());
        setTempSelectedDates(newDates);
      }
    }
  };

  const handlePrevMonth = () => {
    setCurrentDate(subMonths(currentDate, 1));
  };

  const handleNextMonth = () => {
    const nextMonth = addMonths(currentDate, 1);
    const today = new Date();
    // Only allow navigation if the next month is not in the future
    if (nextMonth.getFullYear() < today.getFullYear() || 
        (nextMonth.getFullYear() === today.getFullYear() && nextMonth.getMonth() <= today.getMonth())) {
      setCurrentDate(nextMonth);
    }
  };

  const handleApply = () => {
    if (tempSelectedDates.length === 2) {
      setSelectedDates(tempSelectedDates);
      onApply(tempSelectedDates[0], tempSelectedDates[1]);
      setIsOpen(false);
    }
  };

  const handleCancel = () => {
    setTempSelectedDates(selectedDates);
    setIsOpen(false);
  };

  const generateCalendarDays = () => {
    const start = startOfMonth(currentDate);
    const end = endOfMonth(currentDate);
    return eachDayOfInterval({ start, end });
  };

  const isInRange = (date: Date) => {
    if (tempSelectedDates.length !== 2) return false;
    return date >= tempSelectedDates[0] && date <= tempSelectedDates[1];
  };

  return (
    <div className="relative" style={{fontFamily: 'Poppins, sans-serif'}}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 text-[16px] text-[#2D3134] font-medium"
      >
        {selectedRange === 'Custom' && selectedDates.length === 2
          ? formatDateRange(selectedDates[0], selectedDates[1])
          : selectedRange
        }
        <CaretDown size={16} className={`transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute left-0 sm:right-0 sm:left-auto top-full mt-2 w-[280px] bg-white rounded-lg shadow-lg z-50 border border-gray-100">
          <div className="p-4">
            <div className="space-y-2">
              {['This Month', 'Last Month', 'Custom'].map((option) => (
                <button
                  key={option}
                  onClick={() => handleRangeSelect(option as DateRange)}
                  className={`w-full text-[16px] font-medium text-left px-3 py-2 rounded ${
                    selectedRange === option
                      ? 'bg-green-600/10 !text-green-600'
                      : 'hover:bg-gray-100/20'
                  }`}
                >
                  {option}
                </button>
              ))}
            </div>

            {selectedRange === 'Custom' && (
              <>
                <div className="mt-4">
                  <div className="flex justify-between items-center mb-4">
                    <button 
                      className="p-1 hover:bg-gray-100 rounded-full"
                      onClick={handlePrevMonth}
                    >
                      <CaretLeft size={16} />
                    </button>
                    <span className="text-sm font-medium">
                      {format(currentDate, 'MMMM yyyy')}
                    </span>
                    <button 
                      className={`p-1 rounded-full ${
                        currentDate.getFullYear() === new Date().getFullYear() && 
                        currentDate.getMonth() === new Date().getMonth()
                          ? 'text-gray-300 cursor-not-allowed' 
                          : 'hover:bg-gray-100'
                      }`}
                      onClick={handleNextMonth}
                      disabled={
                        currentDate.getFullYear() === new Date().getFullYear() && 
                        currentDate.getMonth() === new Date().getMonth()
                      }
                    >
                      <CaretRight size={16} />
                    </button>
                  </div>
                  <div className="grid grid-cols-7 gap-1">
                    {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                      <div key={day} className="text-center text-xs text-gray-500 font-medium">
                        {day}
                      </div>
                    ))}
                    {generateCalendarDays().map((date, index) => {
                      const isSelected = tempSelectedDates.some(selectedDate => 
                        isSameDay(selectedDate, date)
                      );
                      const inRange = isInRange(date);
                      const isCurrentMonth = isSameMonth(date, currentDate);
                      const isFutureDate = isAfter(date, new Date());
                      
                      return (
                        <button
                          key={index}
                          onClick={() => handleDateClick(date)}
                          disabled={isFutureDate}
                          className={`
                            p-2 text-sm rounded-full text-center
                            ${!isCurrentMonth ? 'text-gray-300' : ''}
                            ${isFutureDate ? 'text-gray-300 cursor-not-allowed' : ''}
                            ${isSelected ? 'bg-green-600 text-white' : ''}
                            ${inRange && !isSelected ? 'bg-green-600/10' : ''}
                            ${!isFutureDate && !isSelected ? 'hover:bg-gray-50' : ''}
                          `}
                        >
                          {format(date, 'd')}
                        </button>
                      );
                    })}
                  </div>
                </div>
                <div className="flex justify-end gap-2 mt-4">
                  <button
                    onClick={handleCancel}
                    className="px-4 py-2 text-sm text-gray-600 hover:bg-gray-100 rounded"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleApply}
                    disabled={tempSelectedDates.length !== 2}
                    className="px-4 py-2 text-sm bg-[#1C1B1F] text-white rounded disabled:bg-gray-300"
                  >
                    Apply
                  </button>
                </div>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default AnalyticsDateFilter;
